# 劳动力预测Agent数据表设计文档

## Mock数据目标
为data agent的劳动力预测提供完整的数据支撑，实现门店未来销售、客流、人力配置的智能预测。

## 数据表概述
1. **核心业务数据**：
   - 销售数据表（transaction）：记录每笔交易详情
   - 客流数据表（traffic）：记录每小时客流量
   - 商品信息表（product）：商品基础信息

2. **人力资源数据**：
   - 排班数据表（schedule）：员工排班信息
   - 员工信息表（employee）：员工基础信息
   - 考勤记录表（attendance）：员工实际出勤记录

3. **辅助数据**：
   - 门店信息表（store）：门店基础信息
   - 会员信息表（member）：会员基础信息
   - 大促信息表（promotion）：促销活动信息
   - 历史预测记录表（forecast_history）：记录预测结果

## 详细表结构

### 1. 销售数据表（Transaction）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| transaction_id | BIGINT | 主键，自增 | 11201 |
| product_id | VARCHAR(50) | 外键，关联商品表 | P001 |
| store_id | INT | 外键，关联门店表 | 101 |
| salesperson_id | INT | 外键，关联员工表 | 2001 |
| member_id | VARCHAR(50) | 外键，关联会员表，可为NULL | M88001 |
| transaction_datetime | DATETIME | 交易时间 | 2023-10-15 14:30:00 |
| quantity | INT | 商品数量 | 2 |
| unit_price | DECIMAL(10,2) | 单价 | 649.50 |
| total_amount | DECIMAL(10,2) | 总金额 | 1299.00 |
| discount_amount | DECIMAL(10,2) | 折扣金额 | 0.00 |
| payment_method | VARCHAR(20) | 支付方式 | WECHAT_PAY |
| created_at | DATETIME | 创建时间 | 2023-10-15 14:31:05 |

### 2. 客流数据表（Traffic）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| traffic_id | BIGINT | 主键，自增 | 23401 |
| store_id | INT | 外键，NOT NULL | 101 |
| traffic_date | DATE | 统计日期，NOT NULL | 2023-10-16 |
| traffic_hour | TINYINT | 小时数(0-23)，NOT NULL | 10 |
| traffic_count_in | INT | 进店客流数 | 120 |
| traffic_count_out | INT | 出店客流数 | 100 |
| stay_duration_avg | DECIMAL(5,2) | 平均停留时长(分钟) | 45.50 |
| conversion_rate | DECIMAL(5,4) | 转化率 | 0.1250 |
| weather_condition | VARCHAR(50) | 天气状况 | SUNNY |
| temperature | DECIMAL(4,1) | 温度(℃) | 22.5 |
| is_holiday | BOOLEAN | 是否节假日 | TRUE |
| is_weekend | BOOLEAN | 是否周末 | FALSE |
| created_at | DATETIME | 创建时间 | 2023-10-16 11:01:05 |

### 3. 排班数据表（Schedule）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| schedule_id | BIGINT | 主键，自增 | 15001 |
| store_id | INT | 外键，NOT NULL | 101 |
| employee_id | INT | 外键，NOT NULL | 2001 |
| schedule_date | DATE | 排班日期，NOT NULL | 2023-10-16 |
| shift_type | VARCHAR(20) | 班次类型 | morning |
| start_time | VARCHAR(50) | 开始时间，NOT NULL | 09:00:00 |
| end_time | VARCHAR(50) | 结束时间，NOT NULL | 17:00:00 |
| position_code | VARCHAR(30) | 岗位代码，NOT NULL | sales_associate |
| hourly_rate | DECIMAL(8,2) | 小时工资 | 25.50 |
| break_duration | INT | 休息时长(分钟) | 60 |
| is_overtime | BOOLEAN | 是否加班 | FALSE |
| created_at | DATETIME | 创建时间 | 2023-10-15 14:30:15 |

**岗位代码说明**：
- cashier: 收银员
- sales_associate: 导购员
- warehouse_keeper: 库管员
- store_manager: 店长
- assistant_manager: 店长助理

### 4. 商品信息表（Product）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| product_id | VARCHAR(50) | 主键 | P001 |
| product_name | VARCHAR(255) | 商品名称，NOT NULL | 双喜篮球鞋 |
| brand | VARCHAR(100) | 品牌 | 双喜 |
| type | VARCHAR(100) | 商品类型，NOT NULL | 篮球鞋 |
| category | VARCHAR(100) | 商品分类，NOT NULL | 鞋类 |
| price | DECIMAL(10,2) | 售价，NOT NULL | 899.00 |
| cost | DECIMAL(10,2) | 成本价 | 450.00 |
| launch_date | DATE | 上市日期，NOT NULL | 2024-09-01 |
| season | VARCHAR(20) | 季节属性 | AUTUMN |
| is_active | BOOLEAN | 是否在售 | TRUE |
| created_at | DATETIME | 创建时间 | 2024-08-15 10:00:00 |

### 5. 门店信息表（Store）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| store_id | INT | 主键 | 101 |
| store_code | VARCHAR(20) | 门店编码 | SH001 |
| store_name | VARCHAR(255) | 门店名称，NOT NULL | 上海市青浦区百联奥莱广场工厂店 |
| store_type | VARCHAR(100) | 门店类型，NOT NULL | 十人以上店铺 |
| city | VARCHAR(50) | 城市，NOT NULL | 上海 |
| district | VARCHAR(50) | 区域 | 青浦区 |
| address | VARCHAR(500) | 详细地址，NOT NULL | 上海市青浦区沪青平公路百联奥莱广场3层C329 |
| store_area | DECIMAL(10,2) | 门店面积(㎡)，NOT NULL | 80.00 |
| rent_cost | DECIMAL(10,2) | 月租金 | 15000.00 |
| open_date | DATE | 开业日期，NOT NULL | 2019-03-01 |
| in_store_zoning | VARCHAR(500) | 内部分区 | A,B,C |
| operating_hours_start | VARCHAR(50) | 营业开始时间 | 10:00:00 |
| operating_hours_end | VARCHAR(50) | 营业结束时间 | 22:00:00 |
| is_active | BOOLEAN | 是否营业 | TRUE |
| created_at | DATETIME | 创建时间 | 2019-02-15 10:00:00 |

**门店类型说明**：
- 十人以内店铺: <10人
- 十人以上店铺: 10-30人
- 大型单层店铺(30人以上): >30人单层
- 大型多楼层店铺(30人以上): >30人多层

### 6. 员工信息表（Employee）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| employee_id | INT | 主键，自增 | 2001 |
| employee_code | VARCHAR(20) | 员工编号 | EMP001 |
| name | VARCHAR(100) | 姓名，NOT NULL | 王婷 |
| gender | ENUM('M','F') | 性别 | F |
| age | INT | 年龄 | 25 |
| phone | VARCHAR(20) | 手机号 | 13812345678 |
| email | VARCHAR(100) | 邮箱 | <EMAIL> |
| hire_date | DATE | 入职日期 | 2022-03-01 |
| position | VARCHAR(30) | 主要岗位 | sales_associate |
| level | VARCHAR(20) | 职级 | junior |
| base_salary | DECIMAL(8,2) | 基本工资 | 4500.00 |
| commission_rate | DECIMAL(5,4) | 提成比例 | 0.0200 |
| store_id | INT | 所属门店 | 101 |
| status | ENUM('ACTIVE','INACTIVE','RESIGNED') | 状态 | ACTIVE |
| created_at | DATETIME | 创建时间 | 2022-02-15 10:00:00 |

### 7. 会员信息表（Member）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| member_id | VARCHAR(50) | 主键 | M88001 |
| member_code | VARCHAR(20) | 会员编号 | VIP001 |
| name | VARCHAR(100) | 姓名 | 张三 |
| phone | VARCHAR(20) | 手机号 | 13900000001 |
| gender | ENUM('M','F') | 性别 | M |
| age | INT | 年龄 | 28 |
| register_date | DATE | 注册日期 | 2023-01-15 |
| level | VARCHAR(20) | 会员等级 | GOLD |
| total_consumption | DECIMAL(10,2) | 累计消费 | 15600.00 |
| points | INT | 积分余额 | 1560 |
| preferred_store_id | INT | 常去门店 | 101 |
| status | ENUM('ACTIVE','INACTIVE') | 状态 | ACTIVE |
| created_at | DATETIME | 创建时间 | 2023-01-15 10:00:00 |

### 8. 大促信息表（Promotion）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| promotion_id | BIGINT | 主键，自增 | 20001 |
| promotion_code | VARCHAR(30) | 促销编码，UNIQUE | NATIONAL_DAY_2023 |
| promotion_name | VARCHAR(100) | 促销名称，NOT NULL | 国庆黄金周8折大促 |
| store_id | INT | 门店ID，NULL表示全店适用 | 101 |
| promotion_type | VARCHAR(20) | 促销类型，NOT NULL | DISCOUNT |
| discount_rate | DECIMAL(5,2) | 折扣率 | 0.80 |
| min_amount | DECIMAL(10,2) | 最低消费金额 | 500.00 |
| max_discount | DECIMAL(10,2) | 最大折扣金额 | 200.00 |
| start_date | DATE | 开始日期，NOT NULL | 2023-09-30 |
| end_date | DATE | 结束日期，NOT NULL | 2023-10-07 |
| expected_traffic_boost | DECIMAL(5,2) | 预期客流提升比例 | 1.30 |
| expected_sales_boost | DECIMAL(5,2) | 预期销售提升比例 | 1.50 |
| status | ENUM('PENDING','ACTIVE','EXPIRED','CANCELLED') | 状态 | ACTIVE |
| created_at | DATETIME | 创建时间 | 2023-09-25 10:30:00 |

### 9. 考勤记录表（Attendance）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| attendance_id | BIGINT | 主键，自增 | 50001 |
| employee_id | INT | 外键，关联员工表 | 2001 |
| store_id | INT | 外键，关联门店表 | 101 |
| schedule_id | BIGINT | 外键，关联排班表 | 15001 |
| attendance_date | DATE | 考勤日期，NOT NULL | 2023-10-16 |
| scheduled_start_time | VARCHAR(50) | 计划上班时间 | 09:00:00 |
| scheduled_end_time | VARCHAR(50) | 计划下班时间 | 17:00:00 |
| actual_start_time | VARCHAR(50) | 实际上班时间 | 09:05:00 |
| actual_end_time | VARCHAR(50) | 实际下班时间 | 17:05:00 |
| status | VARCHAR(20) | 考勤状态 | LATE |
| work_hours | DECIMAL(4,1) | 实际工作时长 | 7.5 |
| overtime_hours | DECIMAL(4,1) | 加班时长 | 0.5 |
| late_minutes | INT | 迟到分钟数 | 5 |
| early_leave_minutes | INT | 早退分钟数 | 0 |
| created_at | DATETIME | 创建时间 | 2023-10-16 17:05:00 |

**考勤状态说明**：
- NORMAL: 正常
- LATE: 迟到
- EARLY_LEAVE: 早退
- ABSENT: 缺勤
- OVERTIME: 加班

### 10. 历史预测记录表（Forecast_History）
| 字段名 | 数据类型 | 约束/说明 | 示例数据 |
|--------|----------|-----------|----------|
| forecast_id | BIGINT | 主键，自增 | 60001 |
| store_id | INT | 外键，关联门店表 | 101 |
| forecast_date | DATE | 预测目标日期 | 2023-10-20 |
| forecast_hour | TINYINT | 预测目标小时 | 14 |
| model_version | VARCHAR(20) | 预测模型版本 | v1.2.0 |
| predicted_traffic | INT | 预测客流量 | 150 |
| predicted_sales | DECIMAL(10,2) | 预测销售额 | 15000.00 |
| recommended_staff_count | INT | 建议配置人数 | 5 |
| recommended_positions | JSON | 建议岗位配置 | {"cashier":1,"sales_associate":3,"manager":1} |
| forecast_confidence | DECIMAL(5,2) | 预测置信度(0-1) | 0.85 |
| weather_forecast | VARCHAR(50) | 预测天气 | SUNNY |
| temperature_forecast | DECIMAL(4,1) | 预测温度 | 23.0 |
| is_holiday | BOOLEAN | 是否节假日 | FALSE |
| is_weekend | BOOLEAN | 是否周末 | TRUE |
| has_promotion | BOOLEAN | 是否有促销活动 | TRUE |
| promotion_impact_factor | DECIMAL(5,2) | 促销影响系数 | 1.25 |
| actual_traffic | INT | 实际客流量(回测用) | 145 |
| actual_sales | DECIMAL(10,2) | 实际销售额(回测用) | 14500.00 |
| actual_staff_count | INT | 实际配置人数(回测用) | 4 |
| accuracy_score | DECIMAL(5,2) | 预测准确度 | 0.92 |
| forecast_created_at | DATETIME | 预测生成时间 | 2023-10-15 10:00:00 |
| created_at | DATETIME | 记录创建时间 | 2023-10-15 10:00:00 |

## 数据关系说明

### 主要外键关系
- `transaction.store_id` → `store.store_id`
- `transaction.product_id` → `product.product_id`
- `transaction.salesperson_id` → `employee.employee_id`
- `transaction.member_id` → `member.member_id`
- `traffic.store_id` → `store.store_id`
- `schedule.store_id` → `store.store_id`
- `schedule.employee_id` → `employee.employee_id`
- `attendance.schedule_id` → `schedule.schedule_id`
- `forecast_history.store_id` → `store.store_id`

### 预测模型关键指标
1. **客流预测因子**：历史客流、天气、节假日、促销活动、季节性
2. **销售预测因子**：历史销售、客流量、商品组合、促销力度、会员占比
3. **人力配置因子**：预测客流、预测销售、岗位需求、成本控制

## 门店组织架构

| 门店规模 | 管理层 | 职能岗 | 销售岗 | 收银岗 |
|----------|--------|--------|--------|--------|
| 小型店铺(<10人) | 店长 | 无 | 店员 | 收银员 |
| 中型店铺(10-30人) | 店长、副店长 | 无 | 店员 | 收银员 |
| 大型店铺(>30人) | 店长、副店长、区域组长 | 库管、陈列专员、产品专员 | 店员 | 收银员 |


