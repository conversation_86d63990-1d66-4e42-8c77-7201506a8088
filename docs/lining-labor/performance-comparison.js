const mysql = require('mysql2/promise');
const fs = require('fs');
const csv = require('csv-parser');
const chalk = require('chalk');

/**
 * 性能对比测试：新旧导入方法对比
 */

const dbConfig = {
    host: '************',
    port: 3306,
    user: 'dhr_data_anlaysis',
    password: 'dhr_data_anlaysis',
    database: 'dhr_data_anlaysis',
    charset: 'utf8mb4'
};

/**
 * 原始导入方法（逐行插入）
 */
async function oldImportMethod(connection, tableName, data) {
    console.log(chalk.yellow(`🐌 原始方法：逐行插入 ${data.length} 行到 ${tableName}`));
    
    const startTime = Date.now();
    const columns = Object.keys(data[0]);
    const sql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES (${columns.map(() => '?').join(',')})`;
    
    let insertedCount = 0;
    let errorCount = 0;
    
    for (let i = 0; i < data.length; i++) {
        try {
            const values = columns.map(col => data[i][col]);
            await connection.execute(sql, values);
            insertedCount++;
            
            // 显示进度（每100行）
            if ((i + 1) % 100 === 0) {
                const progress = Math.round(((i + 1) / data.length) * 100);
                const speed = Math.round(((i + 1) / (Date.now() - startTime)) * 1000);
                process.stdout.write(`\r   进度: ${progress}% - ${speed} 行/秒`);
            }
        } catch (error) {
            errorCount++;
        }
    }
    
    const duration = Date.now() - startTime;
    const speed = Math.round((insertedCount / duration) * 1000);
    
    console.log(`\n   结果: ${insertedCount} 行成功，${errorCount} 行失败，耗时 ${duration}ms，速度 ${speed} 行/秒`);
    
    return { insertedCount, errorCount, duration, speed };
}

/**
 * 新的批量导入方法
 */
async function newImportMethod(connection, tableName, data) {
    console.log(chalk.green(`🚀 新方法：批量插入 ${data.length} 行到 ${tableName}`));
    
    const startTime = Date.now();
    const columns = Object.keys(data[0]);
    const batchSize = 1000;
    
    let insertedCount = 0;
    let errorCount = 0;
    
    await connection.beginTransaction();
    
    try {
        for (let i = 0; i < data.length; i += batchSize) {
            const batch = data.slice(i, i + batchSize);
            
            try {
                // 构建批量插入SQL
                const placeholders = batch.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
                const sql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES ${placeholders}`;
                const values = batch.flatMap(row => columns.map(col => row[col]));
                
                await connection.execute(sql, values);
                insertedCount += batch.length;
                
                // 显示进度
                const progress = Math.round((insertedCount / data.length) * 100);
                const speed = Math.round((insertedCount / (Date.now() - startTime)) * 1000);
                process.stdout.write(`\r   进度: ${progress}% - ${speed} 行/秒`);
                
            } catch (error) {
                // 批量失败时尝试逐行插入
                const singleSql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES (${columns.map(() => '?').join(',')})`;
                for (const row of batch) {
                    try {
                        const rowValues = columns.map(col => row[col]);
                        await connection.execute(singleSql, rowValues);
                        insertedCount++;
                    } catch (rowError) {
                        errorCount++;
                    }
                }
            }
        }
        
        await connection.commit();
        
    } catch (error) {
        await connection.rollback();
        throw error;
    }
    
    const duration = Date.now() - startTime;
    const speed = Math.round((insertedCount / duration) * 1000);
    
    console.log(`\n   结果: ${insertedCount} 行成功，${errorCount} 行失败，耗时 ${duration}ms，速度 ${speed} 行/秒`);
    
    return { insertedCount, errorCount, duration, speed };
}

/**
 * 解析CSV数据
 */
async function parseCSVData(filePath, maxRows = null) {
    return new Promise((resolve, reject) => {
        const data = [];
        let rowCount = 0;
        
        fs.createReadStream(filePath)
            .pipe(csv())
            .on('data', (row) => {
                if (maxRows && rowCount >= maxRows) {
                    return;
                }
                
                // 数据类型转换
                const cleanedRow = {};
                for (const [key, value] of Object.entries(row)) {
                    let cleanedValue = value;
                    
                    if (value === '' || value === null || value === undefined || value === 'null') {
                        cleanedValue = null;
                    } else if (value === 'true') {
                        cleanedValue = true;
                    } else if (value === 'false') {
                        cleanedValue = false;
                    } else if (!isNaN(value) && value.trim() !== '') {
                        const numValue = parseFloat(value);
                        if (Number.isInteger(numValue) && Math.abs(numValue) <= Number.MAX_SAFE_INTEGER) {
                            cleanedValue = parseInt(value);
                        } else {
                            cleanedValue = numValue;
                        }
                    }
                    
                    cleanedRow[key] = cleanedValue;
                }
                
                data.push(cleanedRow);
                rowCount++;
            })
            .on('end', () => resolve(data))
            .on('error', reject);
    });
}

/**
 * 主测试函数
 */
async function runPerformanceComparison() {
    console.log(chalk.blue('🏁 CSV导入性能对比测试'));
    console.log(chalk.blue('=' .repeat(60)));
    
    let connection = null;
    
    try {
        // 连接数据库
        console.log(chalk.gray('🔌 连接数据库...'));
        connection = await mysql.createConnection(dbConfig);
        await connection.ping();
        console.log(chalk.green('✅ 数据库连接成功\n'));
        
        // 测试用例配置
        const testCases = [
            {
                file: './output/store.csv',
                table: 'test_store',
                name: '门店数据',
                maxRows: null // 全部数据
            },
            {
                file: './output/product.csv',
                table: 'test_product',
                name: '商品数据',
                maxRows: null // 全部数据
            },
            {
                file: './output/employee.csv',
                table: 'test_employee',
                name: '员工数据',
                maxRows: null // 全部数据
            },
            {
                file: './output/member.csv',
                table: 'test_member',
                name: '会员数据',
                maxRows: 500 // 限制行数以节省时间
            },
            {
                file: './output/transaction.csv',
                table: 'test_transaction',
                name: '交易数据',
                maxRows: 1000 // 限制行数以节省时间
            }
        ];
        
        const results = [];
        
        for (const testCase of testCases) {
            if (!fs.existsSync(testCase.file)) {
                console.log(chalk.yellow(`⚠️  文件不存在，跳过: ${testCase.file}`));
                continue;
            }
            
            console.log(chalk.blue(`\n📊 测试 ${testCase.name} (${testCase.file})`));
            console.log(chalk.gray('-'.repeat(50)));
            
            // 解析数据
            console.log(chalk.gray('📖 解析CSV数据...'));
            const data = await parseCSVData(testCase.file, testCase.maxRows);
            console.log(chalk.gray(`✅ 解析完成：${data.length} 行\n`));
            
            if (data.length === 0) {
                console.log(chalk.yellow('⚠️  没有数据，跳过测试\n'));
                continue;
            }
            
            // 创建测试表
            const originalTable = testCase.table.replace('test_', 'lining_labor_');
            try {
                await connection.execute(`DROP TABLE IF EXISTS ${testCase.table}`);
                await connection.execute(`CREATE TABLE ${testCase.table} LIKE ${originalTable}`);
            } catch (error) {
                console.log(chalk.red(`❌ 创建测试表失败: ${error.message}`));
                continue;
            }
            
            // 测试原始方法（限制数据量以节省时间）
            const testData = data.slice(0, Math.min(data.length, 200));
            console.log(chalk.gray(`使用 ${testData.length} 行数据进行测试\n`));
            
            await connection.execute(`DELETE FROM ${testCase.table}`);
            const oldResult = await oldImportMethod(connection, testCase.table, testData);
            
            console.log(); // 空行
            
            // 测试新方法
            await connection.execute(`DELETE FROM ${testCase.table}`);
            const newResult = await newImportMethod(connection, testCase.table, testData);
            
            // 计算性能提升
            const speedImprovement = newResult.speed / oldResult.speed;
            const timeReduction = ((oldResult.duration - newResult.duration) / oldResult.duration) * 100;
            
            results.push({
                name: testCase.name,
                dataSize: testData.length,
                oldMethod: oldResult,
                newMethod: newResult,
                speedImprovement: speedImprovement,
                timeReduction: timeReduction
            });
            
            console.log(chalk.cyan(`\n📈 性能提升: ${speedImprovement.toFixed(2)}x 更快`));
            console.log(chalk.cyan(`⏱️  时间节省: ${timeReduction.toFixed(1)}%`));
            
            // 清理测试表
            await connection.execute(`DROP TABLE ${testCase.table}`);
        }
        
        // 显示总体结果
        console.log(chalk.blue('\n🏆 性能对比总结'));
        console.log(chalk.blue('=' .repeat(60)));
        
        if (results.length > 0) {
            const avgSpeedImprovement = results.reduce((sum, r) => sum + r.speedImprovement, 0) / results.length;
            const avgTimeReduction = results.reduce((sum, r) => sum + r.timeReduction, 0) / results.length;
            
            console.log(chalk.white('测试结果详情:'));
            results.forEach(result => {
                console.log(chalk.white(`📋 ${result.name}:`));
                console.log(chalk.gray(`   数据量: ${result.dataSize} 行`));
                console.log(chalk.gray(`   原方法: ${result.oldMethod.speed} 行/秒`));
                console.log(chalk.gray(`   新方法: ${result.newMethod.speed} 行/秒`));
                console.log(chalk.green(`   提升: ${result.speedImprovement.toFixed(2)}x 更快`));
                console.log();
            });
            
            console.log(chalk.green(`🎯 平均性能提升: ${avgSpeedImprovement.toFixed(2)}x 更快`));
            console.log(chalk.green(`⚡ 平均时间节省: ${avgTimeReduction.toFixed(1)}%`));
            
            // 估算大数据量的时间节省
            const estimatedOldTime = Math.round(1316008 / (results[0].oldMethod.speed / 1000)); // 秒
            const estimatedNewTime = Math.round(1316008 / (results[0].newMethod.speed / 1000)); // 秒
            const timeSaved = estimatedOldTime - estimatedNewTime;
            
            console.log(chalk.blue(`\n📊 对于131万行数据的估算:`));
            console.log(chalk.white(`   原方法预计耗时: ${Math.floor(estimatedOldTime / 60)}分${estimatedOldTime % 60}秒`));
            console.log(chalk.white(`   新方法预计耗时: ${Math.floor(estimatedNewTime / 60)}分${estimatedNewTime % 60}秒`));
            console.log(chalk.green(`   节省时间: ${Math.floor(timeSaved / 60)}分${timeSaved % 60}秒`));
        }
        
        console.log(chalk.blue('=' .repeat(60)));
        console.log(chalk.green('✨ 性能对比测试完成！'));
        
    } catch (error) {
        console.error(chalk.red('❌ 测试失败:'), error.message);
        console.error(error.stack);
    } finally {
        if (connection) {
            await connection.end();
            console.log(chalk.gray('🔌 数据库连接已关闭'));
        }
    }
}

// 运行测试
if (require.main === module) {
    runPerformanceComparison().catch(console.error);
}

module.exports = { runPerformanceComparison, oldImportMethod, newImportMethod };
