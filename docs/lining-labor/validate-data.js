const fs = require('fs');

// 简单的CSV解析函数
function parseCSV(filePath) {
    const content = fs.readFileSync(filePath, 'utf8');
    const lines = content.trim().split('\n');
    const headers = lines[0].split(',');
    
    return lines.slice(1).map(line => {
        const values = line.split(',');
        const obj = {};
        headers.forEach((header, index) => {
            obj[header] = values[index];
        });
        return obj;
    });
}

console.log('=== 数据完整性验证 ===\n');

try {
    // 读取所有数据
    const stores = parseCSV('./output/store.csv');
    const products = parseCSV('./output/product.csv');
    const employees = parseCSV('./output/employee.csv');
    const members = parseCSV('./output/member.csv');
    const promotions = parseCSV('./output/promotion.csv');
    const schedules = parseCSV('./output/schedule.csv');
    const attendances = parseCSV('./output/attendance.csv');
    const traffic = parseCSV('./output/traffic.csv');
    const transactions = parseCSV('./output/transaction.csv');
    const forecasts = parseCSV('./output/forecast_history.csv');

    console.log('✅ 所有CSV文件读取成功');

    // 验证外键关系
    let errors = 0;

    // 1. 验证员工的store_id是否存在
    const storeIds = new Set(stores.map(s => s.store_id));
    employees.forEach(emp => {
        if (!storeIds.has(emp.store_id)) {
            console.log(`❌ 员工${emp.employee_id}的门店ID${emp.store_id}不存在`);
            errors++;
        }
    });

    // 2. 验证会员的preferred_store_id是否存在
    members.forEach(member => {
        if (member.preferred_store_id && !storeIds.has(member.preferred_store_id)) {
            console.log(`❌ 会员${member.member_id}的首选门店ID${member.preferred_store_id}不存在`);
            errors++;
        }
    });

    // 3. 验证交易数据的外键
    const productIds = new Set(products.map(p => p.product_id));
    const employeeIds = new Set(employees.map(e => e.employee_id));
    const memberIds = new Set(members.map(m => m.member_id));

    let transactionSample = transactions.slice(0, 1000); // 验证前1000条交易
    transactionSample.forEach(trans => {
        if (!storeIds.has(trans.store_id)) {
            console.log(`❌ 交易${trans.transaction_id}的门店ID${trans.store_id}不存在`);
            errors++;
        }
        if (!productIds.has(trans.product_id)) {
            console.log(`❌ 交易${trans.transaction_id}的商品ID${trans.product_id}不存在`);
            errors++;
        }
        if (trans.salesperson_id && !employeeIds.has(trans.salesperson_id)) {
            console.log(`❌ 交易${trans.transaction_id}的销售员ID${trans.salesperson_id}不存在`);
            errors++;
        }
        if (trans.member_id && trans.member_id !== '' && !memberIds.has(trans.member_id)) {
            console.log(`❌ 交易${trans.transaction_id}的会员ID${trans.member_id}不存在`);
            errors++;
        }
    });

    // 4. 验证客流数据的门店ID
    let trafficSample = traffic.slice(0, 100);
    trafficSample.forEach(t => {
        if (!storeIds.has(t.store_id)) {
            console.log(`❌ 客流记录${t.traffic_id}的门店ID${t.store_id}不存在`);
            errors++;
        }
    });

    if (errors === 0) {
        console.log('✅ 外键关系验证通过');
    } else {
        console.log(`❌ 发现${errors}个外键错误`);
    }

    // 验证数据范围合理性
    console.log('\n=== 数据合理性检查 ===');

    // 检查商品价格
    const invalidPrices = products.filter(p => parseFloat(p.price) <= 0 || parseFloat(p.price) > 5000);
    if (invalidPrices.length > 0) {
        console.log(`❌ 发现${invalidPrices.length}个异常商品价格`);
    } else {
        console.log('✅ 商品价格范围合理');
    }

    // 检查交易金额
    const invalidTransactions = transactionSample.filter(t => 
        parseFloat(t.total_amount) <= 0 || parseFloat(t.total_amount) > 50000
    );
    if (invalidTransactions.length > 0) {
        console.log(`❌ 发现${invalidTransactions.length}个异常交易金额`);
    } else {
        console.log('✅ 交易金额范围合理');
    }

    // 检查客流数据
    const invalidTraffic = trafficSample.filter(t => 
        parseInt(t.traffic_count_in) < 0 || parseInt(t.traffic_count_in) > 1000
    );
    if (invalidTraffic.length > 0) {
        console.log(`❌ 发现${invalidTraffic.length}个异常客流数据`);
    } else {
        console.log('✅ 客流数据范围合理');
    }

    // 统计信息
    console.log('\n=== 数据统计摘要 ===');
    console.log(`门店数量: ${stores.length}`);
    console.log(`商品数量: ${products.length}`);
    console.log(`员工数量: ${employees.length}`);
    console.log(`会员数量: ${members.length}`);
    console.log(`促销活动: ${promotions.length}`);
    console.log(`排班记录: ${schedules.length.toLocaleString()}`);
    console.log(`考勤记录: ${attendances.length.toLocaleString()}`);
    console.log(`客流记录: ${traffic.length.toLocaleString()}`);
    console.log(`交易记录: ${transactions.length.toLocaleString()}`);
    console.log(`预测记录: ${forecasts.length}`);

    // 计算总销售额
    const totalSales = transactions.reduce((sum, t) => sum + parseFloat(t.total_amount), 0);
    console.log(`\n总销售额: ¥${(totalSales / 100000000).toFixed(2)}亿元`);
    console.log(`平均客单价: ¥${(totalSales / transactions.length).toFixed(2)}`);

    console.log('\n✅ 数据验证完成！数据质量良好，可用于模型训练。');

} catch (error) {
    console.log('❌ 数据验证失败:', error.message);
}
