const mysql = require('mysql2/promise');
const fs = require('fs');
const csv = require('csv-parser');
const path = require('path');

/**
 * 高效的CSV数据导入工具 - 简化版
 * 性能提升：批量插入比逐行插入快20+倍
 */

// 数据库配置
const dbConfig = {
    host: '************',
    port: 3306,
    user: 'dhr_data_anlaysis',
    password: 'dhr_data_anlaysis',
    database: 'dhr_data_anlaysis',
    charset: 'utf8mb4'
};

// 性能配置
const BATCH_SIZE = 1000;  // 批量插入大小（降低以避免占位符限制）
const MAX_CONNECTIONS = 10;  // 最大连接数

/**
 * 解析CSV文件
 * @param {string} filePath - CSV文件路径
 * @returns {Promise<Array>} 解析后的数据数组
 */
async function parseCSV(filePath) {
    console.log(`📖 正在解析 ${path.basename(filePath)}...`);

    return new Promise((resolve, reject) => {
        const data = [];
        const startTime = Date.now();

        fs.createReadStream(filePath)
            .pipe(csv())
            .on('data', (row) => {
                // 数据类型转换和清理
                const cleanedRow = {};
                for (const [key, value] of Object.entries(row)) {
                    let cleanedValue = value;

                    // 处理空值
                    if (value === '' || value === null || value === undefined || value === 'null') {
                        cleanedValue = null;
                    }
                    // 处理布尔值
                    else if (value === 'true') {
                        cleanedValue = true;
                    } else if (value === 'false') {
                        cleanedValue = false;
                    }
                    // 处理数字
                    else if (!isNaN(value) && value.trim() !== '') {
                        const numValue = parseFloat(value);
                        if (Number.isInteger(numValue) && Math.abs(numValue) <= Number.MAX_SAFE_INTEGER) {
                            cleanedValue = parseInt(value);
                        } else {
                            cleanedValue = numValue;
                        }
                    }

                    cleanedRow[key] = cleanedValue;
                }
                data.push(cleanedRow);
            })
            .on('end', () => {
                const duration = Date.now() - startTime;
                console.log(`✅ 解析完成：${data.length.toLocaleString()} 行，耗时 ${duration}ms`);
                resolve(data);
            })
            .on('error', reject);
    });
}

/**
 * 批量插入数据
 * @param {Object} connection - 数据库连接
 * @param {string} tableName - 表名
 * @param {Array} data - 数据数组
 * @returns {Promise<Object>} 插入结果
 */
async function batchInsert(connection, tableName, data) {
    if (data.length === 0) {
        return { insertedCount: 0, errorCount: 0, duration: 0 };
    }

    console.log(`💾 开始导入 ${tableName}，共 ${data.length.toLocaleString()} 行...`);

    const startTime = Date.now();
    const columns = Object.keys(data[0]);
    let insertedCount = 0;
    let errorCount = 0;

    // 动态调整批量大小以避免占位符限制
    const maxPlaceholders = 65535; // MySQL限制
    const dynamicBatchSize = Math.min(BATCH_SIZE, Math.floor(maxPlaceholders / columns.length));
    console.log(`   使用批量大小: ${dynamicBatchSize} (字段数: ${columns.length})`);

    // 开始事务
    await connection.beginTransaction();

    try {
        for (let i = 0; i < data.length; i += dynamicBatchSize) {
            const batch = data.slice(i, i + dynamicBatchSize);

            try {
                // 构建批量插入SQL
                const placeholders = batch.map(() => `(${columns.map(() => '?').join(',')})`).join(',');
                const sql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES ${placeholders}`;
                const values = batch.flatMap(row => columns.map(col => row[col]));

                await connection.execute(sql, values);
                insertedCount += batch.length;

                // 显示进度
                const progress = Math.round((insertedCount / data.length) * 100);
                const speed = Math.round((insertedCount / (Date.now() - startTime)) * 1000);
                process.stdout.write(`\r   进度: ${progress}% (${insertedCount.toLocaleString()}/${data.length.toLocaleString()}) - ${speed} 行/秒`);

            } catch (error) {
                console.error(`\n❌ 批量插入错误 (第 ${i} 行):`, error.message);

                // 尝试逐行插入
                const singleSql = `INSERT INTO ${tableName} (${columns.join(',')}) VALUES (${columns.map(() => '?').join(',')})`;
                for (const row of batch) {
                    try {
                        const rowValues = columns.map(col => row[col]);
                        await connection.execute(singleSql, rowValues);
                        insertedCount++;
                    } catch (rowError) {
                        errorCount++;
                        if (errorCount <= 10) { // 只显示前10个错误
                            console.error(`   行错误:`, rowError.message);
                        }
                    }
                }
            }
        }

        // 提交事务
        await connection.commit();
        console.log(); // 换行

        const duration = Date.now() - startTime;
        const speed = Math.round((insertedCount / duration) * 1000);

        console.log(`✅ ${tableName} 导入完成：${insertedCount.toLocaleString()} 行成功，${errorCount} 行失败，耗时 ${Math.round(duration/1000)}秒，速度 ${speed} 行/秒`);

        return { insertedCount, errorCount, duration, speed };

    } catch (error) {
        await connection.rollback();
        throw error;
    }
}

/**
 * 主导入函数
 */
async function importAllData() {
    console.log('🚀 高效CSV导入工具 - DHR劳动力预测数据');
    console.log('=' .repeat(60));

    let connection = null;

    try {
        // 连接数据库
        console.log('🔌 连接数据库...');
        connection = await mysql.createConnection(dbConfig);
        await connection.ping();
        console.log('✅ 数据库连接成功！\n');

        // 检查并创建表
        console.log('🔧 检查数据表...');
        const createTableSQL = fs.readFileSync('./output/create_tables.sql', 'utf8');
        const statements = createTableSQL.split(';').filter(stmt => stmt.trim());

        for (const statement of statements) {
            if (statement.trim()) {
                try {
                    await connection.execute(statement);
                } catch (error) {
                    if (!error.message.includes('already exists')) {
                        console.warn('⚠️  表创建警告:', error.message);
                    }
                }
            }
        }
        console.log('✅ 数据表检查完成！\n');

        // 定义导入顺序
        const importTasks = [
            { file: 'store.csv', table: 'lining_labor_store', name: '门店数据' },
            { file: 'product.csv', table: 'lining_labor_product', name: '商品数据' },
            { file: 'employee.csv', table: 'lining_labor_employee', name: '员工数据' },
            { file: 'member.csv', table: 'lining_labor_member', name: '会员数据' },
            { file: 'promotion.csv', table: 'lining_labor_promotion', name: '促销活动数据' },
            { file: 'schedule.csv', table: 'lining_labor_schedule', name: '排班数据' },
            { file: 'attendance.csv', table: 'lining_labor_attendance', name: '考勤数据' },
            { file: 'traffic.csv', table: 'lining_labor_traffic', name: '客流数据' },
            { file: 'transaction.csv', table: 'lining_labor_transaction', name: '交易数据' },
            { file: 'forecast_history.csv', table: 'lining_labor_forecast_history', name: '预测历史数据' }
        ];

        const results = [];
        const overallStartTime = Date.now();

        // 逐个导入文件
        for (let i = 0; i < importTasks.length; i++) {
            const task = importTasks[i];
            const filePath = path.join('./output', task.file);

            console.log(`📁 [${i + 1}/${importTasks.length}] 导入${task.name} (${task.file})`);
            console.log('-'.repeat(50));

            if (!fs.existsSync(filePath)) {
                console.log(`⚠️  文件不存在，跳过: ${task.file}\n`);
                continue;
            }

            try {
                // 解析CSV
                const data = await parseCSV(filePath);

                // 批量插入
                const result = await batchInsert(connection, task.table, data);

                results.push({
                    ...result,
                    file: task.file,
                    table: task.table,
                    name: task.name,
                    success: true
                });

            } catch (error) {
                console.error(`❌ ${task.name} 导入失败:`, error.message);
                results.push({
                    file: task.file,
                    table: task.table,
                    name: task.name,
                    success: false,
                    error: error.message
                });
            }

            console.log(); // 空行分隔
        }

        // 显示总体统计
        const overallDuration = Math.round((Date.now() - overallStartTime) / 1000);
        const successCount = results.filter(r => r.success).length;
        const totalInserted = results.reduce((sum, r) => sum + (r.insertedCount || 0), 0);
        const totalErrors = results.reduce((sum, r) => sum + (r.errorCount || 0), 0);
        const overallSpeed = overallDuration > 0 ? Math.round(totalInserted / overallDuration) : 0;

        console.log('🎉 导入完成！');
        console.log('=' .repeat(60));
        console.log(`✅ 成功导入: ${successCount}/${importTasks.length} 个文件`);
        console.log(`📊 总计插入: ${totalInserted.toLocaleString()} 行`);
        console.log(`❌ 错误记录: ${totalErrors.toLocaleString()} 行`);
        console.log(`⏱️  总耗时: ${Math.floor(overallDuration / 60)}分${overallDuration % 60}秒`);
        console.log(`⚡ 平均速度: ${overallSpeed.toLocaleString()} 行/秒`);
        console.log('=' .repeat(60));

        // 验证导入结果
        console.log('\n🔍 验证导入结果:');
        for (const task of importTasks) {
            try {
                const [rows] = await connection.execute(`SELECT COUNT(*) as count FROM ${task.table}`);
                console.log(`📋 ${task.table}: ${rows[0].count.toLocaleString()} 行`);
            } catch (error) {
                console.log(`❌ ${task.table}: 查询失败`);
            }
        }

        console.log('\n✨ 数据导入完成！');

    } catch (error) {
        console.error('❌ 导入过程出错:', error.message);
        console.error(error.stack);
        process.exit(1);
    } finally {
        if (connection) {
            await connection.end();
            console.log('🔌 数据库连接已关闭');
        }
    }
}

// 如果直接运行此脚本
if (require.main === module) {
    importAllData().catch(console.error);
}

module.exports = { importAllData, parseCSV, batchInsert };
