DROP TABLE IF EXISTS lining_labor_store;


CREATE TABLE lining_labor_store (
    store_id INT PRIMARY KEY COMMENT '门店ID',
    store_code VARCHAR(20) NOT NULL COMMENT '门店编码',
    store_name VARCHAR(255) NOT NULL COMMENT '门店名称',
    store_type VARCHAR(100) NOT NULL COMMENT '门店类型',
    city VARCHAR(50) NOT NULL COMMENT '所在城市',
    district VARCHAR(50) COMMENT '所在区域',
    address VARCHAR(500) NOT NULL COMMENT '详细地址',
    store_area DECIMAL(10,2) NOT NULL COMMENT '门店面积(平方米)',
    rent_cost DECIMAL(10,2) COMMENT '租金成本',
    open_date DATE NOT NULL COMMENT '开业日期',
    in_store_zoning VARCHAR(500) COMMENT '店内分区',
    operating_hours_start VARCHAR(50) COMMENT '营业开始时间',
    operating_hours_end VARCHAR(50) COMMENT '营业结束时间',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否在营业',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '门店信息表';

DROP TABLE IF EXISTS lining_labor_product;


CREATE TABLE lining_labor_product (
    product_id VARCHAR(50) PRIMARY KEY COMMENT '商品ID',
    product_name VARCHAR(255) NOT NULL COMMENT '商品名称',
    brand VARCHAR(100) COMMENT '品牌',
    type VARCHAR(100) NOT NULL COMMENT '商品类型',
    category VARCHAR(100) NOT NULL COMMENT '商品类别',
    price DECIMAL(10,2) NOT NULL COMMENT '售价',
    cost DECIMAL(10,2) COMMENT '成本价',
    launch_date DATE NOT NULL COMMENT '上市日期',
    season VARCHAR(20) COMMENT '季节',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否在售',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '商品信息表';

DROP TABLE IF EXISTS lining_labor_employee;


CREATE TABLE lining_labor_employee (
    employee_id INT PRIMARY KEY COMMENT '员工ID',
    employee_code VARCHAR(20) COMMENT '员工编号',
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    gender ENUM('M','F') COMMENT '性别',
    age INT COMMENT '年龄',
    phone VARCHAR(20) COMMENT '电话',
    email VARCHAR(100) COMMENT '邮箱',
    hire_date DATE COMMENT '入职日期',
    position VARCHAR(30) COMMENT '职位',
    level VARCHAR(20) COMMENT '级别',
    base_salary DECIMAL(8,2) COMMENT '基本工资',
    commission_rate DECIMAL(5,4) COMMENT '提成比例',
    store_id INT COMMENT '所属门店ID',
    status ENUM('ACTIVE','INACTIVE','RESIGNED') DEFAULT 'ACTIVE' COMMENT '在职状态',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '员工信息表';

DROP TABLE IF EXISTS lining_labor_member;


CREATE TABLE lining_labor_member (
    member_id VARCHAR(50) PRIMARY KEY COMMENT '会员ID',
    member_code VARCHAR(20) COMMENT '会员编号',
    name VARCHAR(100) COMMENT '姓名',
    phone VARCHAR(20) COMMENT '电话',
    gender ENUM('M','F') COMMENT '性别',
    age INT COMMENT '年龄',
    register_date DATE COMMENT '注册日期',
    level VARCHAR(20) COMMENT '会员等级',
    total_consumption DECIMAL(10,2) COMMENT '累计消费',
    points INT COMMENT '积分',
    preferred_store_id INT COMMENT '常用门店ID',
    status ENUM('ACTIVE','INACTIVE') DEFAULT 'ACTIVE' COMMENT '会员状态',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '会员信息表';

DROP TABLE IF EXISTS lining_labor_promotion;


CREATE TABLE lining_labor_promotion (
    promotion_id BIGINT PRIMARY KEY COMMENT '促销活动ID',
    promotion_code VARCHAR(30) UNIQUE COMMENT '促销活动编码',
    promotion_name VARCHAR(100) NOT NULL COMMENT '促销活动名称',
    store_id INT COMMENT '适用门店ID',
    promotion_type VARCHAR(20) NOT NULL COMMENT '促销类型',
    discount_rate DECIMAL(5,2) COMMENT '折扣率',
    min_amount DECIMAL(10,2) COMMENT '最低消费金额',
    max_discount DECIMAL(10,2) COMMENT '最高优惠金额',
    start_date DATE NOT NULL COMMENT '开始日期',
    end_date DATE NOT NULL COMMENT '结束日期',
    expected_traffic_boost DECIMAL(5,2) COMMENT '预期客流提升率',
    expected_sales_boost DECIMAL(5,2) COMMENT '预期销售提升率',
    status ENUM('PENDING','ACTIVE','EXPIRED','CANCELLED') DEFAULT 'PENDING' COMMENT '活动状态',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '促销活动表';

DROP TABLE IF EXISTS lining_labor_schedule;


CREATE TABLE lining_labor_schedule (
    schedule_id BIGINT PRIMARY KEY COMMENT '排班ID',
    store_id INT NOT NULL COMMENT '门店ID',
    employee_id INT NOT NULL COMMENT '员工ID',
    schedule_date DATE NOT NULL COMMENT '排班日期',
    shift_type VARCHAR(20) COMMENT '班次类型',
    start_time VARCHAR(50) NOT NULL COMMENT '开始时间',
    end_time VARCHAR(50) NOT NULL COMMENT '结束时间',
    position_code VARCHAR(30) NOT NULL COMMENT '岗位编码',
    hourly_rate DECIMAL(8,2) COMMENT '时薪',
    break_duration INT COMMENT '休息时长(分钟)',
    is_overtime BOOLEAN DEFAULT FALSE COMMENT '是否加班',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '排班信息表';

DROP TABLE IF EXISTS lining_labor_attendance;


CREATE TABLE lining_labor_attendance (
    attendance_id BIGINT PRIMARY KEY COMMENT '考勤ID',
    employee_id INT NOT NULL COMMENT '员工ID',
    store_id INT NOT NULL COMMENT '门店ID',
    schedule_id BIGINT COMMENT '关联排班ID',
    attendance_date DATE NOT NULL COMMENT '考勤日期',
    scheduled_start_time VARCHAR(50) COMMENT '计划上班时间',
    scheduled_end_time VARCHAR(50) COMMENT '计划下班时间',
    actual_start_time VARCHAR(50) COMMENT '实际上班时间',
    actual_end_time VARCHAR(50) COMMENT '实际下班时间',
    status VARCHAR(20) COMMENT '考勤状态',
    work_hours DECIMAL(4,1) COMMENT '工作时长',
    overtime_hours DECIMAL(4,1) COMMENT '加班时长',
    late_minutes INT COMMENT '迟到分钟数',
    early_leave_minutes INT COMMENT '早退分钟数',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '考勤记录表';

DROP TABLE IF EXISTS lining_labor_traffic;


CREATE TABLE lining_labor_traffic (
    traffic_id BIGINT PRIMARY KEY COMMENT '客流ID',
    store_id INT NOT NULL COMMENT '门店ID',
    traffic_date DATE NOT NULL COMMENT '日期',
    traffic_hour TINYINT NOT NULL COMMENT '小时(0-23)',
    traffic_count_in INT COMMENT '进店人数',
    traffic_count_out INT COMMENT '出店人数',
    stay_duration_avg DECIMAL(5,2) COMMENT '平均停留时长(分钟)',
    conversion_rate DECIMAL(5,4) COMMENT '转化率',
    weather_condition VARCHAR(50) COMMENT '天气状况',
    temperature DECIMAL(4,1) COMMENT '温度',
    is_holiday BOOLEAN COMMENT '是否节假日',
    is_weekend BOOLEAN COMMENT '是否周末',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '客流数据表';

DROP TABLE IF EXISTS lining_labor_transaction;


CREATE TABLE lining_labor_transaction (
    transaction_id BIGINT PRIMARY KEY COMMENT '交易ID',
    product_id VARCHAR(50) COMMENT '商品ID',
    store_id INT COMMENT '门店ID',
    salesperson_id INT COMMENT '销售员ID',
    member_id VARCHAR(50) COMMENT '会员ID',
    transaction_datetime DATETIME COMMENT '交易时间',
    quantity INT COMMENT '数量',
    unit_price DECIMAL(10,2) COMMENT '单价',
    total_amount DECIMAL(10,2) COMMENT '总金额',
    discount_amount DECIMAL(10,2) COMMENT '折扣金额',
    payment_method VARCHAR(20) COMMENT '支付方式',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '交易数据表';

DROP TABLE IF EXISTS lining_labor_forecast_history;


CREATE TABLE lining_labor_forecast_history (
    forecast_id BIGINT PRIMARY KEY COMMENT '预测ID',
    store_id INT COMMENT '门店ID',
    forecast_date DATE COMMENT '预测日期',
    forecast_hour TINYINT COMMENT '预测小时(0-23)',
    model_version VARCHAR(20) COMMENT '模型版本',
    predicted_traffic INT COMMENT '预测客流量',
    predicted_sales DECIMAL(10,2) COMMENT '预测销售额',
    recommended_staff_count INT COMMENT '建议员工数',
    recommended_positions JSON COMMENT '建议岗位配置',
    forecast_confidence DECIMAL(5,2) COMMENT '预测置信度',
    weather_forecast VARCHAR(50) COMMENT '天气预报',
    temperature_forecast DECIMAL(4,1) COMMENT '预测温度',
    is_holiday BOOLEAN COMMENT '是否节假日',
    is_weekend BOOLEAN COMMENT '是否周末',
    has_promotion BOOLEAN COMMENT '是否有促销',
    promotion_impact_factor DECIMAL(5,2) COMMENT '促销影响系数',
    actual_traffic INT COMMENT '实际客流量',
    actual_sales DECIMAL(10,2) COMMENT '实际销售额',
    actual_staff_count INT COMMENT '实际员工数',
    accuracy_score DECIMAL(5,2) COMMENT '预测准确度',
    forecast_created_at DATETIME COMMENT '预测生成时间',
    created_at DATETIME COMMENT '创建时间'
) COMMENT '预测历史表';